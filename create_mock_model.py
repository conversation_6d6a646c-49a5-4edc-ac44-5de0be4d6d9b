#!/usr/bin/env python3
"""
创建模拟的模型文件用于测试
"""

import torch
import torch.nn as nn
from torchvision import models
import os
import sys

# 添加项目根目录到路径
sys.path.append('.')

from config.config import Config

def create_mock_medical_model():
    """创建模拟的医学影像模型"""
    config = Config()
    
    # 创建模型
    model = models.resnet50(pretrained=False)
    model.fc = nn.Linear(model.fc.in_features, config.NUM_CLASSES)
    
    # 模拟的类别名称
    class_names = ['t1', 't2', 'flair', 'dwi']
    
    # 保存模型
    model_path = os.path.join(config.CHECKPOINT_DIR, 'resnet50_middle_z_best.pth')
    
    torch.save({
        'model_state_dict': model.state_dict(),
        'class_names': class_names,
        'num_classes': len(class_names),
        'slice_mode': 'middle',
        'axis': 'z',
        'model_name': 'resnet50',
        'best_accuracy': 0.85
    }, model_path)
    
    print(f"✅ 模拟医学影像模型已保存: {model_path}")
    return model_path

def create_mock_legacy_models():
    """创建模拟的旧模型文件"""
    config = Config()
    
    # 创建cor模型
    cor_model = models.resnet50(pretrained=False)
    num_ftrs = cor_model.fc.in_features
    cor_model.fc = nn.Sequential(
        nn.Linear(num_ftrs, 512),
        nn.ReLU(),
        nn.Dropout(0.5),
        nn.Linear(512, config.NUM_CLASSES)
    )
    
    # 创建sag模型
    sag_model = models.resnet50(pretrained=False)
    sag_model.fc = nn.Sequential(
        nn.Linear(num_ftrs, 512),
        nn.ReLU(),
        nn.Dropout(0.5),
        nn.Linear(512, config.NUM_CLASSES)
    )
    
    # 模拟的类别名称
    class_names = ['normal', 'abnormal']
    
    # 保存cor模型
    cor_path = os.path.join(config.CHECKPOINT_DIR, 'cor_best_mode.pth')
    torch.save({
        'model_state_dict': cor_model.state_dict(),
        'class_names': class_names,
        'epoch': 25,
        'accuracy': 0.90
    }, cor_path)
    
    # 保存sag模型
    sag_path = os.path.join(config.CHECKPOINT_DIR, 'sag_best_mode.pth')
    torch.save({
        'model_state_dict': sag_model.state_dict(),
        'class_names': class_names,
        'epoch': 25,
        'accuracy': 0.88
    }, sag_path)
    
    print(f"✅ 模拟旧模型已保存:")
    print(f"   Cor模型: {cor_path}")
    print(f"   Sag模型: {sag_path}")
    
    return cor_path, sag_path

def main():
    """主函数"""
    print("🚀 创建模拟模型文件...")
    
    # 确保检查点目录存在
    config = Config()
    os.makedirs(config.CHECKPOINT_DIR, exist_ok=True)
    
    # 创建模拟模型
    try:
        medical_model_path = create_mock_medical_model()
        cor_path, sag_path = create_mock_legacy_models()
        
        print(f"\n🎉 所有模拟模型创建完成！")
        print(f"现在可以测试重构后的预测服务了。")
        
        return True
    except Exception as e:
        print(f"❌ 创建模拟模型失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
