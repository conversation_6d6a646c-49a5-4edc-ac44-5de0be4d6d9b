import torch
from torchvision import models, transforms
import os
import torch.nn as nn
from PIL import Image
import logging
import numpy as np

logger = logging.getLogger(__name__)

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath('__file__')))

from config.config import Config
from src.utils import PadToSquare
from src.predict_nii import MRSequencePredictor

class ResNet50Predictor:
    def __init__(self):
        """初始化 ResNet-50 模型"""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.cor_model = None
        self.sag_model = None
        self.cor_checkpoint = None
        self.sag_checkpoint = None
        self.class_labels = None
        self.config = Config()

    def _load_trained_model(self, checkpoint_path, freeze=True):
        # 1. 初始化一个未经预训练的 ResNet-50 骨架
        #    如果你想加载 PyTorch 官方的 ImageNet 预训练权重作为基础，
        #    使用 weights=models.ResNet50_Weights.DEFAULT
        model = models.resnet50(weights=None)

        # 2. 修改全连接层以匹配你保存的模型结构
        num_ftrs = model.fc.in_features
        model.fc = nn.Sequential(
            nn.Linear(num_ftrs, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, self.config.NUM_CLASSES)
        )

        # 3. 加载你的权重
        #    strict=False 可以在某些层不匹配时（比如你这次不想加载FC层）避免报错
        #    但在这里，我们期望结构是完全匹配的，所以默认 strict=True 即可

        print(f"正在从 {checkpoint_path} 加载模型权重...")
        checkpoint = torch.load(checkpoint_path, map_location=self.device, weights_only=True)
        model.load_state_dict(checkpoint['model_state_dict'])

        # 4. 根据需要冻结层
        if freeze:
            # 冻结所有层
            for param in model.parameters():
                param.requires_grad = False
            # 然后只解冻新加的全连接层
            # 注意：这里需要迭代 model.fc 的参数
            for param in model.fc.parameters():
                param.requires_grad = True

        # 5. 将模型移动到指定设备
        model.to(self.device)

        return model, checkpoint

    def load_model(self):
        if self.cor_model is None and self.sag_model is None:
            logger.info("Loading ResNet-50 model...")
            cor_checkpoint_file = os.path.join(self.config.CHECKPOINT_DIR, f"cor_best_mode.pth")
            self.cor_model, self.cor_checkpoint = self._load_trained_model(cor_checkpoint_file)

            sag_checkpoint_file = os.path.join(self.config.CHECKPOINT_DIR, f"sag_best_mode.pth")
            self.sag_model, self.sag_checkpoint = self._load_trained_model(sag_checkpoint_file)
            logger.info(f"Model loaded successfully on {self.device}")


    def _predict_image(self, cor_image_obj, model, checkpoint):
        """
        对单张图像进行预测。
        """
        class_names = checkpoint.get('class_names')  # 使用 .get() 更安全
        if not class_names:
            logger.info("警告: Checkpoint 中未找到 'class_names'。")
        # 图像预处理
        transform = transforms.Compose([
            PadToSquare(self.config.IMAGE_SIZE),
            transforms.Grayscale(num_output_channels=3),
            transforms.ToTensor(),
            transforms.Normalize(mean=self.config.IMAGENET_MEAN, std=self.config.IMAGENET_STD)
        ])

        image = cor_image_obj.convert("RGB")
        image_tensor = transform(image).unsqueeze(0).to(self.device)

        # 预测
        model.eval()
        with torch.no_grad():
            outputs = model(image_tensor)
            # 将模型的输出（logits）转换为概率
            probabilities = torch.nn.functional.softmax(outputs, dim=1)

            # --- 使用 torch.max 一步获取最大概率及其索引 ---
            # torch.max(probabilities, 1) 会返回一个元组 (最大值张量, 索引张量)
            confidence_tensor, predicted_idx_tensor = torch.max(probabilities, 1)
            # 使用 .item() 将它们转换为 Python 数字
            confidence = confidence_tensor.item()
            predicted_idx = predicted_idx_tensor.item()

        # 使用加载的 class_names 映射索引到具体的类别名称
        predicted_class = class_names[predicted_idx]

        # 现在您同样拥有了类别名称和对应的概率
        logger.info(f"预测类别: {predicted_class}")
        logger.info(f"置信度: {confidence:.4f}")
        return predicted_idx, confidence

    def predict(self,cor_img_obj: Image, sag_img_obj:Image):
        self.load_model()
        cor_predicted_class, cor_score = self._predict_image(cor_img_obj, self.cor_model, self.cor_checkpoint)
        sag_predicted_class, sag_score = self._predict_image(sag_img_obj, self.sag_model, self.sag_checkpoint)
        if cor_predicted_class == 0 and sag_predicted_class == 0:
            return 0
        elif cor_predicted_class == 0 or sag_predicted_class == 0:
            print(f'cor:{cor_predicted_class}, sag: {sag_predicted_class} 存在差异，取最大得分情况')
            if cor_score > sag_score:
                return cor_predicted_class
            else:
                return sag_predicted_class
        else:
            return 1

    def predict_score(self,cor_img_obj: Image, sag_img_obj:Image):
        self.load_model()
        cor_predicted_class, cor_score = self._predict_image(cor_img_obj, self.cor_model, self.cor_checkpoint)
        sag_predicted_class, sag_score = self._predict_image(sag_img_obj, self.sag_model, self.sag_checkpoint)
        return {
            "cor": cor_predicted_class,
            "cor_score": cor_score,
            "sag": sag_predicted_class,
            "sag_score": sag_score
        }
class MedicalPredictor:
    """
    基于MRSequencePredictor的新预测器，支持医学影像MR序列分类
    同时保持向后兼容性
    """
    def __init__(self, model_path=None, slice_mode='middle', axis='z'):
        """
        初始化医学影像预测器

        Args:
            model_path (str): 模型路径，如果为None则自动查找
            slice_mode (str): 切片模式，'middle' 或 'multi'
            axis (str): 轴向，'x', 'y', 'z'
        """
        self.config = Config()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.mr_predictor = None
        self.model_path = model_path
        self.slice_mode = slice_mode
        self.axis = axis

        # 为了向后兼容，保留旧的属性名
        self.cor_model = None
        self.sag_model = None
        self.cor_checkpoint = None
        self.sag_checkpoint = None
        self.class_labels = None

        logger.info(f"初始化医学影像预测器 - 模式: {slice_mode}, 轴向: {axis}")

    def load_model(self):
        """加载MR序列预测模型"""
        if self.mr_predictor is None:
            try:
                logger.info("加载MR序列预测模型...")
                self.mr_predictor = MRSequencePredictor(
                    model_path=self.model_path,
                    slice_mode=self.slice_mode,
                    axis=self.axis
                )
                self.mr_predictor.load_model()
                logger.info(f"MR序列预测模型加载成功，设备: {self.device}")

                # 为了向后兼容，设置旧的属性
                self.cor_model = self.mr_predictor
                self.sag_model = self.mr_predictor

            except Exception as e:
                logger.error(f"MR序列预测模型加载失败: {e}")
                # 如果新模型加载失败，回退到旧模型
                logger.warning("回退到旧的ResNet50预测器...")
                self._load_legacy_models()

    def _load_legacy_models(self):
        """加载旧的ResNet50模型作为备用"""
        try:
            legacy_predictor = ResNet50Predictor()
            legacy_predictor.load_model()
            self.cor_model = legacy_predictor.cor_model
            self.sag_model = legacy_predictor.sag_model
            self.cor_checkpoint = legacy_predictor.cor_checkpoint
            self.sag_checkpoint = legacy_predictor.sag_checkpoint
            logger.info("旧模型加载成功")
        except Exception as e:
            logger.error(f"旧模型加载也失败: {e}")
            raise

    def predict_from_nii(self, nii_path, return_probabilities=False):
        """
        直接从NII文件进行MR序列预测

        Args:
            nii_path (str): NII文件路径
            return_probabilities (bool): 是否返回概率分布

        Returns:
            dict: 预测结果
        """
        self.load_model()

        if self.mr_predictor is not None:
            try:
                result = self.mr_predictor.predict_nii(nii_path)
                if return_probabilities and 'probabilities' not in result:
                    # 如果需要概率但结果中没有，重新预测
                    result = self.mr_predictor.predictor.predict(nii_path, return_probabilities=True)
                return result
            except Exception as e:
                logger.error(f"MR序列预测失败: {e}")
                return {'error': str(e)}
        else:
            return {'error': '模型未加载'}

    def _array_to_image_obj(self, arr, resize_rect=None):
        """将数组转换为PIL图像对象"""
        if arr.dtype != np.uint8:
            # 归一化到0-255范围
            arr = ((arr - arr.min()) / (arr.max() - arr.min()) * 255).astype(np.uint8)

        rgb_image = Image.fromarray(arr)
        if resize_rect:
            rgb_image = rgb_image.resize(resize_rect, Image.LANCZOS)
        return rgb_image

    def _ct_arr_to_gray(self, arr, window_width=400, window_center=40):
        """CT数组转换为灰度图像"""
        return np.uint8(np.clip(255 * ((arr - window_center) / (window_width) + 1 / 2), 0, 255))

    def predict_from_arrays(self, cor_array, sag_array):
        """
        从cor和sag数组进行预测（向后兼容接口）

        Args:
            cor_array (np.ndarray): 冠状面切片数组
            sag_array (np.ndarray): 矢状面切片数组

        Returns:
            dict: 预测结果
        """
        self.load_model()

        try:
            # 将数组转换为PIL图像
            cor_image_obj = self._array_to_image_obj(cor_array)
            sag_image_obj = self._array_to_image_obj(sag_array)

            if self.mr_predictor is not None:
                # 使用新的MR预测器
                # 注意：新预测器主要设计用于从NII文件预测，这里我们需要适配
                return self._predict_with_mr_predictor(cor_image_obj, sag_image_obj)
            else:
                # 使用旧的预测逻辑
                return self._predict_with_legacy_models(cor_image_obj, sag_image_obj)

        except Exception as e:
            logger.error(f"数组预测失败: {e}")
            return {'error': str(e)}

    def _predict_with_mr_predictor(self, cor_image_obj, sag_image_obj):
        """使用MR预测器进行预测"""
        # 由于MR预测器主要设计用于NII文件，这里我们需要创建一个临时的预测逻辑
        # 或者使用其底层的MedicalImagePredictor
        try:
            if hasattr(self.mr_predictor, 'predictor') and self.mr_predictor.predictor:
                # 创建一个3通道的RGB图像，将cor和sag信息合并
                # 这是一个简化的实现，实际应用中可能需要更复杂的合并策略

                # 将两个图像转换为numpy数组
                cor_arr = np.array(cor_image_obj.convert('L'))  # 转换为灰度
                sag_arr = np.array(sag_image_obj.convert('L'))  # 转换为灰度

                # 创建3通道图像：R=cor, G=sag, B=average
                h, w = max(cor_arr.shape[0], sag_arr.shape[0]), max(cor_arr.shape[1], sag_arr.shape[1])

                # 调整大小到相同尺寸
                cor_resized = np.array(Image.fromarray(cor_arr).resize((w, h), Image.LANCZOS))
                sag_resized = np.array(Image.fromarray(sag_arr).resize((w, h), Image.LANCZOS))

                # 创建3通道图像
                rgb_array = np.stack([
                    cor_resized,
                    sag_resized,
                    (cor_resized + sag_resized) // 2
                ], axis=2)

                rgb_image = Image.fromarray(rgb_array.astype(np.uint8))

                # 使用MedicalImagePredictor的预处理和预测
                input_tensor = self.mr_predictor.predictor.transform(rgb_image).unsqueeze(0).to(self.device)

                with torch.no_grad():
                    outputs = self.mr_predictor.predictor.model(input_tensor)
                    probabilities = torch.nn.functional.softmax(outputs, dim=1)
                    predicted_class_idx = torch.argmax(probabilities, dim=1).item()
                    confidence = probabilities[0][predicted_class_idx].item()

                    predicted_class = self.mr_predictor.predictor.class_names[predicted_class_idx]

                    # 创建概率分布字典
                    prob_dict = {}
                    for i, class_name in enumerate(self.mr_predictor.predictor.class_names):
                        prob_dict[class_name] = probabilities[0][i].item()

                return {
                    'predicted_class': predicted_class,
                    'confidence': confidence,
                    'predicted_class_idx': predicted_class_idx,
                    'probabilities': prob_dict,
                    'slice_mode': self.slice_mode,
                    'axis': self.axis
                }
            else:
                raise Exception("MR预测器未正确初始化")

        except Exception as e:
            logger.error(f"MR预测器预测失败: {e}")
            # 回退到旧的预测方法
            return self._predict_with_legacy_models(cor_image_obj, sag_image_obj)

    def _predict_with_legacy_models(self, cor_image_obj, sag_image_obj):
        """使用旧的ResNet50模型进行预测"""
        if self.cor_model is None or self.sag_model is None:
            raise Exception("旧模型未加载")

        # 使用旧的预测逻辑
        legacy_predictor = ResNet50Predictor()
        legacy_predictor.cor_model = self.cor_model
        legacy_predictor.sag_model = self.sag_model
        legacy_predictor.cor_checkpoint = self.cor_checkpoint
        legacy_predictor.sag_checkpoint = self.sag_checkpoint
        legacy_predictor.config = self.config
        legacy_predictor.device = self.device

        return legacy_predictor.predict_score(cor_image_obj, sag_image_obj)

    # 向后兼容的方法
    def predict(self, cor_img_obj: Image, sag_img_obj: Image):
        """向后兼容的预测方法"""
        result = self.predict_from_arrays(np.array(cor_img_obj), np.array(sag_img_obj))

        if 'error' in result:
            return 0  # 错误情况返回默认值

        # 尝试将新的预测结果转换为旧的格式
        if 'predicted_class' in result:
            # 新格式：MR序列分类
            predicted_class = result['predicted_class']
            # 简化映射：如果是t1或正常类别返回0，其他返回1
            if predicted_class in ['t1', 'normal', 'healthy']:
                return 0
            else:
                return 1
        elif 'cor' in result and 'sag' in result:
            # 旧格式：cor/sag分类
            cor_predicted_class = result['cor']
            sag_predicted_class = result['sag']
            cor_score = result.get('cor_score', 0.5)
            sag_score = result.get('sag_score', 0.5)

            if cor_predicted_class == 0 and sag_predicted_class == 0:
                return 0
            elif cor_predicted_class == 0 or sag_predicted_class == 0:
                if cor_score > sag_score:
                    return cor_predicted_class
                else:
                    return sag_predicted_class
            else:
                return 1
        else:
            return 0

    def predict_score(self, cor_img_obj: Image, sag_img_obj: Image):
        """向后兼容的详细预测方法"""
        result = self.predict_from_arrays(np.array(cor_img_obj), np.array(sag_img_obj))

        if 'error' in result:
            return {
                "cor": 0,
                "cor_score": 0.0,
                "sag": 0,
                "sag_score": 0.0,
                "error": result['error']
            }

        # 如果是新格式的结果，转换为旧格式
        if 'predicted_class' in result:
            predicted_class = result['predicted_class']
            confidence = result.get('confidence', 0.5)

            # 简化映射
            if predicted_class in ['t1', 'normal', 'healthy']:
                class_idx = 0
            else:
                class_idx = 1

            return {
                "cor": class_idx,
                "cor_score": confidence,
                "sag": class_idx,
                "sag_score": confidence,
                "predicted_class": predicted_class,
                "mr_confidence": confidence
            }
        else:
            # 已经是旧格式
            return result


# 创建预测器实例
# 优先使用新的医学预测器，如果失败则回退到旧预测器
try:
    predictor = MedicalPredictor()
    logger.info("使用新的医学影像预测器")
except Exception as e:
    logger.warning(f"新预测器初始化失败，使用旧预测器: {e}")
    predictor = ResNet50Predictor()
