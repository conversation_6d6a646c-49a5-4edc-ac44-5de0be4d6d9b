import io
import json
import numpy as np
from fastapi import FastAPI, HTTPException, File, UploadFile
from pydantic import BaseModel, Field
from fastapi.middleware.cors import CORSMiddleware
from typing import List
from PIL import Image
from .predict import predictor
import traceback
import os
import logging

BASE_DIR = os.path.dirname(os.path.abspath('__file__'))


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# ==============================================================================
# 1. 应用初始化和模型加载
# ==============================================================================

# 初始化 FastAPI 应用
app = FastAPI(
    title="图像预测 API",
    description="一个使用 ResNet-50 模型进行图像分类的 FastAPI 服务。",
    version="1.0.0",
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """应用启动时预加载模型"""
    logger.info("Starting up the application...")
    predictor.load_model()
    logger.info("Application startup completed")
# ==============================================================================
# 2. 定义数据模型 (Pydantic)
# ==============================================================================

# 定义请求体模型
# 接收两个图像，每个图像都是一个三维数组 (height, width, channels)
class ImagePairRequest(BaseModel):
    image1: List[List[List[int]]] = Field(..., description="第一个图像的NumPy数组表示, 格式为 HxWxC")
    image2: List[List[List[int]]] = Field(..., description="第二个图像的NumPy数组表示, 格式为 HxWxC")

class PredictionResult(BaseModel):
    cor: int
    cor_score: float
    sag: int
    sag_score: float

# 新的MR序列预测结果模型
class MRPredictionResult(BaseModel):
    predicted_class: str = Field(..., description="预测的MR序列类型")
    confidence: float = Field(..., description="预测置信度")
    predicted_class_idx: int = Field(..., description="预测类别索引")
    probabilities: dict = Field(..., description="各类别概率分布")
    slice_mode: str = Field(..., description="切片模式")
    axis: str = Field(None, description="轴向")

# 兼容的预测结果模型（包含新旧两种格式）
class EnhancedPredictionResult(BaseModel):
    # 旧格式字段（向后兼容）
    cor: int = Field(None, description="冠状面预测结果")
    cor_score: float = Field(None, description="冠状面置信度")
    sag: int = Field(None, description="矢状面预测结果")
    sag_score: float = Field(None, description="矢状面置信度")

    # 新格式字段
    predicted_class: str = Field(None, description="预测的MR序列类型")
    confidence: float = Field(None, description="预测置信度")
    predicted_class_idx: int = Field(None, description="预测类别索引")
    probabilities: dict = Field(None, description="各类别概率分布")
    slice_mode: str = Field(None, description="切片模式")
    axis: str = Field(None, description="轴向")

    # 元数据
    prediction_mode: str = Field("legacy", description="预测模式：legacy或mr_sequence")

# 定义最终的响应体模型
class PredictionResponse(BaseModel):
    code: int = Field(..., description='200')
    result: PredictionResult
    version: str = Field(None, description="模型版本")

# 增强的响应体模型
class EnhancedPredictionResponse(BaseModel):
    code: int = Field(..., description='200')
    result: EnhancedPredictionResult
    version: str = Field(None, description="模型版本")


# ==============================================================================
# 3. 创建 API 端点
# ==============================================================================
def load_version():
    try:
        with open(os.path.join(BASE_DIR, 'checkpoints', 'version.json'), 'r') as f:
            version = json.load(f)
            return version['version']
    except Exception:
        print(traceback.format_exc())
        return 'not version info'

version = load_version()

@app.post("/predict", response_model=PredictionResponse)
async def predict_images(cor_array_file: UploadFile = File(...), sag_array_file:UploadFile = File(...)):
    """
    接收两个图像数组并返回它们的预测结果。
    线程池中运行它，从而实现并发，不会阻塞服务器的主事件循环。
    """
    try:
        cor_contents = await cor_array_file.read()
        sag_contents = await  sag_array_file.read()
        # 2. 从内存中的二进制数据加载NumPy数组

        cor_buffer = io.BytesIO(cor_contents)
        cor_buffer.seek(0)
        cor_received_array = np.load(cor_buffer)

        sag_buffer = io.BytesIO(sag_contents)
        sag_buffer.seek(0)
        sag_received_array = np.load(sag_buffer)

        cor_image_obj = Image.fromarray(cor_received_array)
        sag_image_obj = Image.fromarray(sag_received_array)
        result = predictor.predict_score(cor_image_obj, sag_image_obj)
    except RuntimeError as e:
        # 如果 get_prediction 内部发生错误，返回 500 错误
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        print(traceback.format_exc())
        # 捕获其他可能的错误，例如 Pydantic 验证失败等
        raise HTTPException(status_code=400, detail=f"请求处理失败: {e}")

    return PredictionResponse(code=200, result=result, version=version)


@app.post("/predict/enhanced", response_model=EnhancedPredictionResponse)
async def predict_images_enhanced(cor_array_file: UploadFile = File(...), sag_array_file: UploadFile = File(...)):
    """
    增强的预测接口，支持新的MR序列分类，同时保持向后兼容
    """
    try:
        cor_contents = await cor_array_file.read()
        sag_contents = await sag_array_file.read()

        # 从内存中的二进制数据加载NumPy数组
        cor_buffer = io.BytesIO(cor_contents)
        cor_buffer.seek(0)
        cor_received_array = np.load(cor_buffer)

        sag_buffer = io.BytesIO(sag_contents)
        sag_buffer.seek(0)
        sag_received_array = np.load(sag_buffer)

        # 使用新的预测器进行预测
        result = predictor.predict_from_arrays(cor_received_array, sag_received_array)

        # 构建增强的响应
        enhanced_result = EnhancedPredictionResult()

        if 'error' in result:
            raise HTTPException(status_code=500, detail=result['error'])

        # 检查是否是新格式的结果
        if 'predicted_class' in result:
            # 新格式：MR序列分类
            enhanced_result.predicted_class = result['predicted_class']
            enhanced_result.confidence = result.get('confidence', 0.0)
            enhanced_result.predicted_class_idx = result.get('predicted_class_idx', 0)
            enhanced_result.probabilities = result.get('probabilities', {})
            enhanced_result.slice_mode = result.get('slice_mode', 'middle')
            enhanced_result.axis = result.get('axis')
            enhanced_result.prediction_mode = "mr_sequence"

            # 为向后兼容，也填充旧格式字段
            if 'mr_confidence' in result:
                enhanced_result.cor = result.get('cor', 0)
                enhanced_result.cor_score = result.get('mr_confidence', 0.0)
                enhanced_result.sag = result.get('sag', 0)
                enhanced_result.sag_score = result.get('mr_confidence', 0.0)
        else:
            # 旧格式：cor/sag分类
            enhanced_result.cor = result.get('cor', 0)
            enhanced_result.cor_score = result.get('cor_score', 0.0)
            enhanced_result.sag = result.get('sag', 0)
            enhanced_result.sag_score = result.get('sag_score', 0.0)
            enhanced_result.prediction_mode = "legacy"

            # 如果有MR序列信息，也填充
            if 'predicted_class' in result:
                enhanced_result.predicted_class = result['predicted_class']
                enhanced_result.confidence = result.get('mr_confidence', 0.0)

        return EnhancedPredictionResponse(code=200, result=enhanced_result, version=version)

    except RuntimeError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=f"请求处理失败: {e}")


@app.post("/predict/nii", response_model=EnhancedPredictionResponse)
async def predict_nii_file(nii_file: UploadFile = File(...),
                          slice_mode: str = "middle",
                          axis: str = "z",
                          return_probabilities: bool = True):
    """
    直接从NII文件进行MR序列预测的新接口
    """
    try:
        # 保存上传的NII文件到临时位置
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix='.nii.gz') as temp_file:
            contents = await nii_file.read()
            temp_file.write(contents)
            temp_nii_path = temp_file.name

        try:
            # 使用新的预测器进行预测
            if hasattr(predictor, 'predict_from_nii'):
                result = predictor.predict_from_nii(temp_nii_path, return_probabilities=return_probabilities)
            else:
                raise HTTPException(status_code=501, detail="当前预测器不支持直接NII文件预测")

            if 'error' in result:
                raise HTTPException(status_code=500, detail=result['error'])

            # 构建响应
            enhanced_result = EnhancedPredictionResult(
                predicted_class=result.get('predicted_class'),
                confidence=result.get('confidence', 0.0),
                predicted_class_idx=result.get('predicted_class_idx', 0),
                probabilities=result.get('probabilities', {}),
                slice_mode=result.get('slice_mode', slice_mode),
                axis=result.get('axis', axis),
                prediction_mode="mr_sequence"
            )

            return EnhancedPredictionResponse(code=200, result=enhanced_result, version=version)

        finally:
            # 清理临时文件
            import os
            if os.path.exists(temp_nii_path):
                os.unlink(temp_nii_path)

    except RuntimeError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=f"请求处理失败: {e}")


# ==============================================================================
# 5. (可选) 添加一个根路径用于健康检查
# ==============================================================================
@app.get("/")
def read_root():
    return {"status": "ok", "message": "欢迎使用医学影像MR序列分类 API"}

@app.get("/health")
def health_check():
    """健康检查端点"""
    try:
        # 检查预测器是否正常
        predictor_status = "ok" if predictor else "error"
        return {
            "status": "ok",
            "predictor": predictor_status,
            "version": version,
            "message": "医学影像预测服务运行正常"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "version": version
        }