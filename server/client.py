import time
import requests
import numpy as np
import SimpleITK as sitk
import io
from PIL import Image
import argparse
import os

# API URLs
API_URL = "http://127.0.0.1:8080/predict"
ENHANCED_API_URL = "http://127.0.0.1:8080/predict/enhanced"
NII_API_URL = "http://127.0.0.1:8080/predict/nii"

def image2d_to_obj(arr, resize_rect=None):
    rgb_image = Image.fromarray(arr)
    if resize_rect:
        rgb_image = rgb_image.resize(resize_rect, Image.LANCZOS)
    return rgb_image


def ct_arr_to_gray(arr, window_width=400, window_center=40):
    return np.uint8(np.clip(255 * ((arr - window_center) / (window_width) + 1 / 2), 0, 255))


def cut_nii_to_image_obj(nii_path):
    image = sitk.ReadImage(nii_path)
    arr = sitk.GetArrayFromImage(image)
    depth, height, width = arr.shape
    spacing_width, spacing_height, spacing_depth = image.GetSpacing()
    arr = arr[::-1, :, :]
    arr1 = arr[:, int(height / 2), :]
    arr2 = arr[:, :, int(width / 2)]

    resize_rect = (width, int(depth * spacing_depth / spacing_width))
    cor_img_obj = image2d_to_obj(ct_arr_to_gray(arr1), resize_rect=resize_rect)

    resize_rect = (height, int(depth * spacing_depth / spacing_height))
    sag_img_obj = image2d_to_obj(ct_arr_to_gray(arr2), resize_rect=resize_rect)
    return cor_img_obj, sag_img_obj


def predict_nii_legacy(nii_pth):
    """使用旧的API接口进行预测（向后兼容）"""
    print("\n--- 使用旧API接口发送请求 (multipart/form-data) ---")
    try:
        cor_img_obj, sag_img_obj = cut_nii_to_image_obj(nii_pth)
        cor_array = np.array(cor_img_obj)
        sag_array = np.array(sag_img_obj)

        # 将NumPy数组序列化到内存
        cor_buffer = io.BytesIO()
        np.save(cor_buffer, cor_array)
        cor_buffer.seek(0)

        sag_buffer = io.BytesIO()
        np.save(sag_buffer, sag_array)
        sag_buffer.seek(0)

        files = {
            'cor_array_file': ('cor_slice.npy', cor_buffer.getvalue(), 'application/x-numpy'),
            'sag_array_file': ('sag_slice.npy', sag_buffer.getvalue(), 'application/x-numpy'),
        }
        start_time = time.time()
        response = requests.post(API_URL, files=files, timeout=30)
        response.raise_for_status()
        result = response.json()
        print(f"\n✅ 旧API请求成功！, 耗时：{(time.time() - start_time):.6f}秒")
        print("服务器返回结果:")
        print(result)
        return result
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 旧API请求失败: {e}")
        return None


def predict_nii_enhanced(nii_pth):
    """使用增强的API接口进行预测"""
    print("\n--- 使用增强API接口发送请求 (multipart/form-data) ---")
    try:
        cor_img_obj, sag_img_obj = cut_nii_to_image_obj(nii_pth)
        cor_array = np.array(cor_img_obj)
        sag_array = np.array(sag_img_obj)

        # 将NumPy数组序列化到内存
        cor_buffer = io.BytesIO()
        np.save(cor_buffer, cor_array)
        cor_buffer.seek(0)

        sag_buffer = io.BytesIO()
        np.save(sag_buffer, sag_array)
        sag_buffer.seek(0)

        files = {
            'cor_array_file': ('cor_slice.npy', cor_buffer.getvalue(), 'application/x-numpy'),
            'sag_array_file': ('sag_slice.npy', sag_buffer.getvalue(), 'application/x-numpy'),
        }
        start_time = time.time()
        response = requests.post(ENHANCED_API_URL, files=files, timeout=30)
        response.raise_for_status()
        result = response.json()
        print(f"\n✅ 增强API请求成功！, 耗时：{(time.time() - start_time):.6f}秒")
        print("服务器返回结果:")
        print_enhanced_result(result)
        return result
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 增强API请求失败: {e}")
        return None


def predict_nii_direct(nii_pth, slice_mode='middle', axis='z', return_probabilities=True):
    """直接上传NII文件进行预测"""
    print(f"\n--- 直接上传NII文件进行预测 (slice_mode={slice_mode}, axis={axis}) ---")
    try:
        with open(nii_pth, 'rb') as f:
            files = {
                'nii_file': (os.path.basename(nii_pth), f, 'application/octet-stream')
            }
            params = {
                'slice_mode': slice_mode,
                'axis': axis,
                'return_probabilities': return_probabilities
            }

            start_time = time.time()
            response = requests.post(NII_API_URL, files=files, params=params, timeout=60)
            response.raise_for_status()
            result = response.json()
            print(f"\n✅ 直接NII预测成功！, 耗时：{(time.time() - start_time):.6f}秒")
            print("服务器返回结果:")
            print_enhanced_result(result)
            return result
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 直接NII预测失败: {e}")
        return None
    except FileNotFoundError:
        print(f"\n❌ 文件不存在: {nii_pth}")
        return None


def print_enhanced_result(result):
    """打印增强的预测结果"""
    if not result or 'result' not in result:
        print("无效的结果")
        return

    res = result['result']
    print(f"📊 预测模式: {res.get('prediction_mode', 'unknown')}")

    # 如果有MR序列预测结果
    if res.get('predicted_class'):
        print(f"🔬 预测序列: {res['predicted_class']}")
        print(f"📈 置信度: {res.get('confidence', 0):.4f}")
        print(f"📐 切片模式: {res.get('slice_mode', 'unknown')}")
        if res.get('axis'):
            print(f"📏 轴向: {res['axis']}")

        if res.get('probabilities'):
            print("\n📊 概率分布:")
            for seq_type, prob in res['probabilities'].items():
                print(f"   {seq_type}: {prob:.4f}")

    # 如果有传统的cor/sag结果
    if res.get('cor') is not None:
        print(f"\n🔍 传统预测结果:")
        print(f"   冠状面: {res['cor']} (置信度: {res.get('cor_score', 0):.4f})")
        print(f"   矢状面: {res['sag']} (置信度: {res.get('sag_score', 0):.4f})")


def predict_nii(nii_pth, mode='auto'):
    """
    统一的预测接口

    Args:
        nii_pth (str): NII文件路径
        mode (str): 预测模式 - 'auto', 'legacy', 'enhanced', 'direct'
    """
    if mode == 'auto':
        # 自动模式：优先尝试直接NII预测，失败则回退到增强API，最后回退到旧API
        print(f"🚀 自动模式预测: {os.path.basename(nii_pth)}")

        # 首先尝试直接NII预测
        result = predict_nii_direct(nii_pth)
        if result:
            return result

        print("⚠️ 直接NII预测失败，尝试增强API...")
        result = predict_nii_enhanced(nii_pth)
        if result:
            return result

        print("⚠️ 增强API失败，尝试旧API...")
        result = predict_nii_legacy(nii_pth)
        return result

    elif mode == 'legacy':
        return predict_nii_legacy(nii_pth)
    elif mode == 'enhanced':
        return predict_nii_enhanced(nii_pth)
    elif mode == 'direct':
        return predict_nii_direct(nii_pth)
    else:
        print(f"❌ 不支持的预测模式: {mode}")
        return None


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='医学影像MR序列预测客户端')
    parser.add_argument('-f', '--file', required=True, help='NII文件路径')
    parser.add_argument('-m', '--mode', default='auto',
                       choices=['auto', 'legacy', 'enhanced', 'direct'],
                       help='预测模式: auto(自动), legacy(旧API), enhanced(增强API), direct(直接NII)')
    parser.add_argument('--slice_mode', default='middle',
                       choices=['middle', 'multi'],
                       help='切片模式 (仅用于direct模式)')
    parser.add_argument('--axis', default='z',
                       choices=['x', 'y', 'z'],
                       help='轴向 (仅用于direct模式的multi切片)')

    args = parser.parse_args()

    print(f"🔬 医学影像MR序列预测")
    print(f"📁 文件: {args.file}")
    print(f"⚙️ 模式: {args.mode}")

    if args.mode == 'direct':
        print(f"📐 切片模式: {args.slice_mode}")
        print(f"📏 轴向: {args.axis}")
        result = predict_nii_direct(args.file, args.slice_mode, args.axis)
    else:
        result = predict_nii(args.file, args.mode)

    if result:
        print(f"\n🎉 预测完成！")
    else:
        print(f"\n❌ 预测失败！")