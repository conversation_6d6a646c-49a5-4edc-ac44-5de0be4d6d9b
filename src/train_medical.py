#!/usr/bin/env python3
"""
医学影像MR序列分类训练脚本

这个脚本专门用于训练医学影像MR序列分类模型，支持识别t1、t2、flair、dwi等序列类型。
"""

import os
import sys
import time
import copy
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim import lr_scheduler
import matplotlib.pyplot as plt
from torchvision import models

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import Config
from src.data_loader import get_medical_data_loaders
from src.utils import save_checkpoint


def load_local_pretrained_model(model_name, model, config):
    """
    从本地model/目录加载预训练模型权重

    Args:
        model_name (str): 模型名称
        model: PyTorch模型实例
        config: 配置对象

    Returns:
        bool: 是否成功加载本地模型
    """
    # 构建本地模型文件路径
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    local_model_path = os.path.join(project_root, 'model', f'{model_name}.pth')

    if not os.path.exists(local_model_path):
        print(f"本地模型文件不存在: {local_model_path}")
        return False

    print(f"从本地加载预训练模型: {local_model_path}")
    try:
        # 加载预训练权重
        pretrained_dict = torch.load(local_model_path, map_location=config.device)
        model_dict = model.state_dict()

        # 过滤掉不匹配的键（如分类层）
        pretrained_dict = {k: v for k, v in pretrained_dict.items()
                         if k in model_dict and model_dict[k].shape == v.shape}

        # 更新模型权重
        model_dict.update(pretrained_dict)
        model.load_state_dict(model_dict)
        print(f"成功加载 {len(pretrained_dict)} 个预训练层")
        return True

    except Exception as e:
        print(f"加载本地模型失败: {e}")
        return False


def train_medical_model(config, slice_mode='middle', axis='z', model_name='resnet50'):
    """
    训练医学影像分类模型
    
    Args:
        config: 配置对象
        slice_mode (str): 切片模式，'middle' 或 'multi'
        axis (str): 当slice_mode='multi'时指定轴向
        model_name (str): 模型名称
    """
    print("=" * 60)
    print("医学影像MR序列分类训练")
    print("=" * 60)
    print(f"切片模式: {slice_mode}")
    if slice_mode == 'multi':
        print(f"切片轴向: {axis}")
    print(f"模型: {model_name}")
    print(f"设备: {config.device}")
    
    # 创建数据加载器
    print("\n加载数据集...")
    data_loaders, class_names, dataset_sizes = get_medical_data_loaders(
        config, 
        slice_mode=slice_mode, 
        axis=axis
    )
    
    if not data_loaders:
        print("错误: 未找到数据集")
        return None
    
    # 更新配置中的类别数量
    config.NUM_CLASSES = len(class_names)
    
    # 创建模型
    print(f"\n创建模型: {model_name}")
    if model_name == 'resnet50':
        # 首先创建不带预训练权重的模型
        model = models.resnet50(pretrained=False)

        # 尝试从本地加载预训练权重，失败则回退到在线下载
        if not load_local_pretrained_model(model_name, model, config):
            print("回退到在线下载预训练模型...")
            model = models.resnet50(pretrained=True)

        # 修改最后的分类层
        num_ftrs = model.fc.in_features
        model.fc = nn.Linear(num_ftrs, config.NUM_CLASSES)

    elif model_name == 'resnet18':
        # 首先创建不带预训练权重的模型
        model = models.resnet18(pretrained=False)

        # 尝试从本地加载预训练权重，失败则回退到在线下载
        if not load_local_pretrained_model(model_name, model, config):
            print("回退到在线下载预训练模型...")
            model = models.resnet18(pretrained=True)

        # 修改最后的分类层
        num_ftrs = model.fc.in_features
        model.fc = nn.Linear(num_ftrs, config.NUM_CLASSES)

    else:
        raise ValueError(f"不支持的模型: {model_name}")
    
    model = model.to(config.device)
    
    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=config.LEARNING_RATE)
    
    # 学习率调度器
    scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
    
    # 训练模型
    print(f"\n开始训练，共 {config.NUM_EPOCHS} 个epoch...")
    model = train_model(
        model, data_loaders, criterion, optimizer, scheduler,
        config, class_names, slice_mode, axis, model_name
    )
    
    return model


def train_model(model, data_loaders, criterion, optimizer, scheduler, 
                config, class_names, slice_mode, axis, model_name):
    """训练模型的核心函数"""
    since = time.time()
    
    best_model_wts = copy.deepcopy(model.state_dict())
    best_acc = 0.0
    
    # 记录训练历史
    train_acc_history = []
    val_acc_history = []
    train_loss_history = []
    val_loss_history = []
    
    for epoch in range(config.NUM_EPOCHS):
        print(f'\nEpoch {epoch+1}/{config.NUM_EPOCHS}')
        print('-' * 40)
        
        # 每个epoch都有训练和验证阶段
        for phase in ['train', 'val']:
            if phase not in data_loaders:
                continue
                
            if phase == 'train':
                model.train()  # 设置模型为训练模式
            else:
                model.eval()   # 设置模型为评估模式
            
            running_loss = 0.0
            running_corrects = 0
            
            # 遍历数据
            for batch_idx, (inputs, labels) in enumerate(data_loaders[phase]):
                inputs = inputs.to(config.device)
                labels = labels.to(config.device)
                
                # 清零参数梯度
                optimizer.zero_grad()
                
                # 前向传播
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    _, preds = torch.max(outputs, 1)
                    loss = criterion(outputs, labels)
                    
                    # 反向传播 + 优化（仅在训练阶段）
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()
                
                # 统计
                running_loss += loss.item() * inputs.size(0)
                running_corrects += torch.sum(preds == labels.data)
                
                # 打印进度
                if batch_idx % 10 == 0:
                    print(f'  批次 {batch_idx}/{len(data_loaders[phase])}, '
                          f'损失: {loss.item():.4f}')
            
            if phase == 'train':
                scheduler.step()
            
            epoch_loss = running_loss / len(data_loaders[phase].dataset)
            epoch_acc = running_corrects.double() / len(data_loaders[phase].dataset)
            
            print(f'{phase.upper()} 损失: {epoch_loss:.4f} 准确率: {epoch_acc:.4f}')
            
            # 记录历史
            if phase == 'train':
                train_acc_history.append(epoch_acc.cpu().numpy())
                train_loss_history.append(epoch_loss)
            else:
                val_acc_history.append(epoch_acc.cpu().numpy())
                val_loss_history.append(epoch_loss)
            
            # 深拷贝模型（如果是最佳模型）
            if phase == 'val' and epoch_acc > best_acc:
                best_acc = epoch_acc
                best_model_wts = copy.deepcopy(model.state_dict())
                
                # 保存最佳模型
                model_filename = f"{model_name}_{slice_mode}_{axis}_best.pth"
                save_checkpoint(
                    model, optimizer, epoch, best_acc.item(), 
                    class_names, config.CHECKPOINT_DIR, model_filename
                )
    
    time_elapsed = time.time() - since
    print(f'\n训练完成，耗时 {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'最佳验证准确率: {best_acc:.4f}')
    
    # 加载最佳模型权重
    model.load_state_dict(best_model_wts)
    
    # 绘制训练历史
    plot_training_history(
        train_acc_history, val_acc_history, 
        train_loss_history, val_loss_history,
        slice_mode, axis
    )
    
    return model


def plot_training_history(train_acc, val_acc, train_loss, val_loss, slice_mode, axis):
    """绘制训练历史曲线"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # 准确率曲线
    ax1.plot(train_acc, label='训练准确率')
    ax1.plot(val_acc, label='验证准确率')
    ax1.set_title('模型准确率')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('准确率')
    ax1.legend()
    ax1.grid(True)
    
    # 损失曲线
    ax2.plot(train_loss, label='训练损失')
    ax2.plot(val_loss, label='验证损失')
    ax2.set_title('模型损失')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('损失')
    ax2.legend()
    ax2.grid(True)
    
    plt.suptitle(f'训练历史 - {slice_mode}模式 {axis}轴')
    plt.tight_layout()
    
    # 保存图像
    save_path = f"training_history_{slice_mode}_{axis}.png"
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"训练历史图已保存到: {save_path}")
    plt.close()


def main():
    """主函数"""
    config = Config()
    
    # 可以尝试不同的配置
    configurations = [
        ('middle', 'z', 'resnet50'),
        ('multi', 'z', 'resnet50'),
    ]
    
    for slice_mode, axis, model_name in configurations:
        print(f"\n{'='*60}")
        print(f"训练配置: {slice_mode}模式, {axis}轴, {model_name}模型")
        print(f"{'='*60}")
        
        try:
            model = train_medical_model(
                config, 
                slice_mode=slice_mode, 
                axis=axis, 
                model_name=model_name
            )
            
            if model is not None:
                print(f"✓ 训练完成: {slice_mode}_{axis}_{model_name}")
            else:
                print(f"✗ 训练失败: {slice_mode}_{axis}_{model_name}")
                
        except Exception as e:
            print(f"✗ 训练出错: {e}")


if __name__ == "__main__":
    main()
