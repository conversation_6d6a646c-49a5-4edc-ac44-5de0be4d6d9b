#!/usr/bin/env python3
"""
测试重构后的预测服务
"""

import sys
import os
import numpy as np
from PIL import Image
import tempfile
import logging

# 添加项目根目录到路径
sys.path.append('.')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_predictor_import():
    """测试预测器导入"""
    try:
        from server.predict import predictor
        logger.info(f"✅ 预测器导入成功，类型: {type(predictor).__name__}")
        return True
    except Exception as e:
        logger.error(f"❌ 预测器导入失败: {e}")
        return False

def test_predictor_initialization():
    """测试预测器初始化"""
    try:
        from server.predict import MedicalPredictor
        
        # 测试不同的初始化方式
        logger.info("测试MedicalPredictor初始化...")
        
        # 1. 默认初始化（可能会失败，因为没有模型文件）
        try:
            predictor = MedicalPredictor()
            logger.info("✅ 默认初始化成功")
        except Exception as e:
            logger.warning(f"⚠️ 默认初始化失败（预期的，因为没有模型文件）: {e}")
        
        # 2. 指定不存在的模型路径
        try:
            predictor = MedicalPredictor(model_path="non_existent_model.pth")
            logger.info("✅ 指定模型路径初始化成功")
        except Exception as e:
            logger.warning(f"⚠️ 指定模型路径初始化失败（预期的）: {e}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 预测器初始化测试失败: {e}")
        return False

def test_legacy_predictor():
    """测试旧的预测器"""
    try:
        from server.predict import ResNet50Predictor
        
        logger.info("测试ResNet50Predictor...")
        predictor = ResNet50Predictor()
        logger.info("✅ ResNet50Predictor初始化成功")
        
        # 测试模型加载（可能会失败）
        try:
            predictor.load_model()
            logger.info("✅ 旧模型加载成功")
        except Exception as e:
            logger.warning(f"⚠️ 旧模型加载失败（预期的，因为没有模型文件）: {e}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 旧预测器测试失败: {e}")
        return False

def test_api_models():
    """测试API数据模型"""
    try:
        from server.main import (
            PredictionResult, 
            MRPredictionResult, 
            EnhancedPredictionResult,
            PredictionResponse,
            EnhancedPredictionResponse
        )
        
        logger.info("测试API数据模型...")
        
        # 测试旧格式
        old_result = PredictionResult(cor=0, cor_score=0.8, sag=1, sag_score=0.9)
        logger.info(f"✅ 旧格式结果: {old_result}")
        
        # 测试新格式
        new_result = MRPredictionResult(
            predicted_class="t1",
            confidence=0.85,
            predicted_class_idx=0,
            probabilities={"t1": 0.85, "t2": 0.15},
            slice_mode="middle"
        )
        logger.info(f"✅ 新格式结果: {new_result}")
        
        # 测试增强格式
        enhanced_result = EnhancedPredictionResult(
            cor=0, cor_score=0.8, sag=1, sag_score=0.9,
            predicted_class="t1", confidence=0.85,
            predicted_class_idx=0,
            probabilities={"t1": 0.85, "t2": 0.15},
            slice_mode="middle",
            prediction_mode="mr_sequence"
        )
        logger.info(f"✅ 增强格式结果: {enhanced_result}")
        
        return True
    except Exception as e:
        logger.error(f"❌ API数据模型测试失败: {e}")
        return False

def test_array_prediction():
    """测试数组预测功能"""
    try:
        from server.predict import predictor
        
        logger.info("测试数组预测功能...")
        
        # 创建模拟的cor和sag数组
        cor_array = np.random.randint(0, 255, (224, 224), dtype=np.uint8)
        sag_array = np.random.randint(0, 255, (224, 224), dtype=np.uint8)
        
        # 测试predict_from_arrays方法（如果存在）
        if hasattr(predictor, 'predict_from_arrays'):
            try:
                result = predictor.predict_from_arrays(cor_array, sag_array)
                logger.info(f"✅ 数组预测成功: {result}")
            except Exception as e:
                logger.warning(f"⚠️ 数组预测失败（预期的，因为没有模型）: {e}")
        else:
            logger.info("⚠️ 预测器没有predict_from_arrays方法")
        
        # 测试旧的predict_score方法
        if hasattr(predictor, 'predict_score'):
            try:
                cor_img = Image.fromarray(cor_array)
                sag_img = Image.fromarray(sag_array)
                result = predictor.predict_score(cor_img, sag_img)
                logger.info(f"✅ 旧格式预测成功: {result}")
            except Exception as e:
                logger.warning(f"⚠️ 旧格式预测失败（预期的，因为没有模型）: {e}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 数组预测测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    try:
        from config.config import Config
        
        config = Config()
        logger.info(f"✅ 配置加载成功")
        logger.info(f"   检查点目录: {config.CHECKPOINT_DIR}")
        logger.info(f"   医学数据目录: {config.MEDICAL_DATA_DIR}")
        logger.info(f"   类别数量: {config.NUM_CLASSES}")
        logger.info(f"   MR序列: {config.MR_SEQUENCES}")
        logger.info(f"   设备: {config.device}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始测试重构后的预测服务")
    
    tests = [
        ("配置测试", test_config),
        ("预测器导入测试", test_predictor_import),
        ("预测器初始化测试", test_predictor_initialization),
        ("旧预测器测试", test_legacy_predictor),
        ("API数据模型测试", test_api_models),
        ("数组预测测试", test_array_prediction),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"📊 测试结果: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
